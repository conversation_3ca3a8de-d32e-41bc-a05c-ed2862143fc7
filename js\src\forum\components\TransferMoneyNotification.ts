import app from 'flarum/forum/app';
import Notification from 'flarum/forum/components/Notification';
import type NotificationModel from 'flarum/common/models/Notification';

export default class TransferMoneyNotification extends Notification<{ notification: NotificationModel }> {
  icon(): string {
    return 'fas fa-money-bill';
  }

  href(): string {
    const user = app.session.user;
    const username = user ? user.username() : '';
    return app.route('user.transferHistory', { username });
  }

  content(): any {
    const user = this.attrs.notification.fromUser();
    return app.translator.trans(
      'wusong8899-transfer-money.forum.notifications.user-transfer-money-to-you',
      {
        user,
      }
    );
  }

  excerpt(): any {
    const notification = this.attrs.notification.subject() as any;
    const transferMoney = notification && notification.attribute ? notification.attribute('transfer_money_value') : undefined;
    const transferID = notification && notification.attribute ? notification.attribute('id') : undefined;
    const moneyName = app.forum.attribute('antoinefr-money.moneyname') || '[money]';
    const costText = moneyName.replace('[money]', transferMoney);

    return app.translator.trans(
      'wusong8899-transfer-money.forum.notifications.user-transfer-money-details',
      {
        cost: costText,
        id: transferID,
      }
    );
  }
}

