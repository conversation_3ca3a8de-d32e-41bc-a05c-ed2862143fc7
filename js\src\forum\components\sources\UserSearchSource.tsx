import type Mithril from 'mithril';
import username from 'flarum/common/helpers/username';
import avatar from 'flarum/common/helpers/avatar';
import highlight from 'flarum/common/helpers/highlight';

export default class UserSearchSource {
  private results: any[] = [];

  async search(query: string): Promise<void> {
    if (!query || query.length < 3) {
      this.results = [];
      return;
    }

    const results = await app.store.find('users', { filter: { q: query }, page: { limit: 5 } });
    this.results = results;
  }

  view(query: string): Array<Mithril.Vnode> {
    const results = this.results;

    if (!results || !results.length) return [] as any;

    return results.map((user: any) => (
      <li className="SearchResult" data-index={`users:${user.id()}`}>
        <a className="SearchResult" tabindex="-1">
          {avatar(user)} {username(user)}
          <span className="SearchResult-excerpt">{highlight(user.username(), query)}</span>
        </a>
      </li>
    )) as any;
  }
}

