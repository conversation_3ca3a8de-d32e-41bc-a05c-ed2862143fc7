import UserPage from 'flarum/components/UserPage';
import TransferHistoryList from './TransferHistoryList';
import type Mithril from 'mithril';

export default class TransferHistoryPage extends UserPage {
  oninit(vnode: Mithril.Vnode<any, this>): void {
    super.oninit(vnode);
    this.loadUser(m.route.param('username'));
  }

  content(): JSX.Element {
    return (
      <div className="TransferHistoryPage">
        {TransferHistoryList.component({
          params: {
            user: this.user,
          },
        })}
      </div>
    );
  }
}

