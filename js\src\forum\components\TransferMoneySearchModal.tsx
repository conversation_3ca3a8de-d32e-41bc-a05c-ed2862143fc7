import app from 'flarum/forum/app';
import Search from 'flarum/forum/components/Search';
import UserSearchSource from './sources/UserSearchSource';
import ItemList from 'flarum/common/utils/ItemList';
import classList from 'flarum/common/utils/classList';
import extractText from 'flarum/common/utils/extractText';
import LoadingIndicator from 'flarum/common/components/LoadingIndicator';
import username from 'flarum/common/helpers/username';
import avatar from 'flarum/common/helpers/avatar';
import type Mithril from 'mithril';

export default class TransferMoneySearchModal extends Search<any> {
  inputUuid!: string;
  typingTimer?: number;
  doSearch = false;

  oninit(vnode: Mithril.Vnode<any, this>): void {
    super.oninit(vnode);
    this.inputUuid = Math.random().toString(36).substring(2);
  }

  oncreate(vnode: Mithril.VnodeDOM<any, this>): void {
    super.oncreate(vnode);

    const $search = this as any;

    this.$('.Search-results').on('click', () => {
      const target = this.$('.SearchResult.active');

      $search.addRecipient(target.data('index'));
      $search.$('.RecipientsInput').focus();
    });

    this.$('.Search-results').on('touchstart', (e: any) => {
      const target = this.$(e.target.parentNode);

      $search.addRecipient(target.data('index'));
      $search.$('.RecipientsInput').focus();
    });

    (window as any).$('.RecipientsInput')
      .on('input', () => {
        clearTimeout(this.typingTimer);
        this.doSearch = false;
        this.typingTimer = window.setTimeout(() => {
          this.doSearch = true;
          m.redraw();
        }, 900);
      })
      .on('keydown', () => {
        clearTimeout(this.typingTimer);
      });

    super.oncreate(vnode);
  }

  view() {
    if (typeof this.searchState.getValue() === 'undefined') {
      this.searchState.setValue('');
    }

    const loading = this.searchState.getValue() && this.searchState.getValue().length >= 3;

    if (!this.sources) {
      this.sources = this.sourceItems().toArray();
    }

    const selectedUserArray = this.attrs.selected().toArray();

    return (
      <div role="search" className="Search">
        <div className="RecipientsInput-selected RecipientsLabel" aria-live="polite">
          <div style="padding-bottom:10px;font-weight:bold;font-size: 14px;color: var(--text-color);">
            {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-to-user')}
          </div>

          {selectedUserArray.length === 0 && (
            <div style="height:34px;cursor: default !important;" class="transferSearchUserContainer">
              {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-no-user-selected')}
            </div>
          )}

          {this.attrs
            .selected()
            .toArray()
            .map((recipient: any) => {
              const userName = username(recipient);
              const userAvatar = avatar(recipient);
              const userID = recipient.data.id;
              this.attrs.selectedUsers[userID] = 1;

              return (
                <div class="transferSearchUserContainer" onclick={(e: MouseEvent) => this.removeRecipient(recipient, e)}>
                  <span class="transferSearchUser">{userAvatar}</span> {userName}
                </div>
              );
            })}
        </div>

        <div className="Form-group">
          <label htmlFor={`transfer-money-user-search-input-${this.inputUuid}`}>
            {app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user')}
          </label>

          <div className="AddRecipientModal-form-input Search-input">
            <input
              id={`transfer-money-user-search-input-${this.inputUuid}`}
              className={classList('RecipientsInput', 'FormControl', {
                open: !!this.searchState.getValue(),
                focused: !!this.searchState.getValue(),
                active: !!this.searchState.getValue(),
                loading: !!this.loadingSources,
              })}
              type="search"
              placeholder={extractText(
                app.translator.trans('wusong8899-transfer-money.forum.transfer-money-search-user-placeholder')
              )}
              value={this.searchState.getValue()}
              oninput={(e: any) => this.searchState.setValue(e.target.value)}
              onfocus={() => (this.hasFocus = true)}
              onblur={() => (this.hasFocus = false)}
            />
            <ul className={classList('Dropdown-menu', 'Search-results', 'fade', { in: !!loading })}>
              {!this.doSearch
                ? LoadingIndicator.component({ size: 'tiny', className: 'Button Button--icon Button--link' })
                : (this.sources as any).map((source: any) => source.view(this.searchState.getValue()))}
            </ul>
          </div>
        </div>
      </div>
    );
  }

  sourceItems() {
    const items = new ItemList();
    items.add('users', new UserSearchSource());
    return items;
  }

  addRecipient(value: string) {
    const values = value.split(':');
    const type = values[0];
    const id = values[1];
    const recipient = this.findRecipient(type, id);
    const userID = recipient.data.id;

    this.attrs.selected().add(value, recipient);
    this.attrs.selectedUsers[userID] = 1;
    this.searchState.clear();
    this.attrs.needMoney(this.getNeedMoney());
    this.attrs.callback();
  }

  removeRecipient(recipient: any, e: Event) {
    e.preventDefault();

    const userID = recipient.data.id;
    delete this.attrs.selectedUsers[userID];

    const type = 'users';
    this.attrs.selected().remove(type + ':' + recipient.id());
    this.attrs.needMoney(this.getNeedMoney());
    this.attrs.callback();
  }

  getNeedMoney() {
    const moneyTransferValue = (window as any).$('#moneyTransferInput').val();
    return moneyTransferValue * Object.keys(this.attrs.selectedUsers).length;
  }

  findRecipient(store: string, id: string) {
    return app.store.getById(store, id);
  }
}

