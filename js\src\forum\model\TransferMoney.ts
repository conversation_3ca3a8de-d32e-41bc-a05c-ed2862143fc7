import Model from 'flarum/common/Model';

// Model for transfer money records
export default class Transfer<PERSON>oney extends Model {}

// Map API attributes/relationships to model accessors
Object.assign(TransferMoney.prototype, {
  // id is provided by the API
  id: Model.attribute<string | undefined>('id'),
  transferMoney: Model.attribute<number>('transfer_money_value'),
  notes: Model.attribute<string | null>('notes'),
  assignedAt: Model.attribute<string>('assigned_at'),
  fromUser: Model.hasOne('fromUser'),
  targetUser: Model.hasOne('targetUser'),
});

