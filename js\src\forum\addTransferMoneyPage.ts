import { extend } from 'flarum/common/extend';
import LinkButton from 'flarum/components/LinkButton';
import UserPage from 'flarum/components/UserPage';
import TransferHistoryPage from './components/TransferHistoryPage';

export default function addTransferMoneyPage(): void {
  (app as any).routes['user.transferHistory'] = {
    component: TransferHistoryPage,
    path: '/u/:username/transferHistory',
  };

  extend(UserPage.prototype, 'navItems', function addNavItem(items: any) {
    if (app.session.user) {
      const currentUserID = app.session.user.id();
      const targetUserID = this.user.id();

      if (currentUserID === targetUserID) {
        items.add(
          'transferMoney',
          LinkButton.component(
            {
              href: app.route('user.transferHistory', {
                username: this.user.username(),
              }),
              icon: 'fas fa-money-bill',
            },
            [app.translator.trans('wusong8899-transfer-money.forum.transfer-money-history')]
          ),
          10
        );
      }
    }
  });
}

